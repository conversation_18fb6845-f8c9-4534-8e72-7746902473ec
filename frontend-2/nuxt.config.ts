// https://nuxt.com/docs/api/configuration/nuxt-config
import Aura from "@primeuix/themes/aura";
import tailwindcss from "@tailwindcss/vite";

export default defineNuxtConfig({
  compatibilityDate: "2025-05-15",
  devtools: { enabled: true },

  // CSS Configuration
  css: [
    "~/assets/css/main.css",
    "~/assets/scss/styles.scss",
    "primeicons/primeicons.css"
  ],

  // Vite Configuration
  vite: {
    plugins: [tailwindcss()],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "~/assets/scss/variables.scss" as *;'
        }
      }
    }
  },

  // Modules
  modules: [
    "@primevue/nuxt-module",
    "@pinia/nuxt"
  ],

  // PrimeVue Configuration
  primevue: {
    options: {
      theme: {
        preset: Aura,
        options: {
          darkModeSelector: '.app-dark',
          cssLayer: {
            name: "primevue",
            order: "theme, base, primevue",
          },
        },
      },
      ripple: true,
    },
    components: {
      include: '*',
      exclude: ['Editor', 'Chart']
    },
    directives: {
      include: ['Tooltip', 'StyleClass', 'Ripple']
    }
  },

  // Runtime Configuration
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:8080/api'
    }
  },

  // App Configuration
  app: {
    head: {
      title: 'LapXpert Admin Dashboard',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'LapXpert Admin Dashboard - Quản lý laptop chuyên nghiệp' }
      ]
    }
  },

  // Auto-imports Configuration
  imports: {
    dirs: [
      'composables/**',
      'stores/**',
      'utils/**'
    ]
  }
});
